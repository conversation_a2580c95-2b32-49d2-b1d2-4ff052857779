import { NextRequest, NextResponse } from 'next/server';

export function middleware(req: NextRequest) {
  // Skip static files and API routes
  if (req.nextUrl.pathname.startsWith('/_next/') ||
      req.nextUrl.pathname.startsWith('/api/') ||
      req.nextUrl.pathname.includes('.')) {
    return NextResponse.next();
  }

  // Define public routes that don't require authentication
  const publicRoutes = ['/login', '/pending-approval'];
  const isPublicRoute = publicRoutes.some(route => req.nextUrl.pathname.startsWith(route));

  // For public routes, allow access
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // For all other routes, let the client-side AuthContext handle the routing
  // This is more reliable with Supabase auth than trying to handle it in middleware
  // The AuthContext will properly redirect users based on their approval status
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
